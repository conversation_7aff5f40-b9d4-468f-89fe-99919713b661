import 'package:flutter/material.dart';
import 'package:web_app/database/database_screens/discussions_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_screens/epublishing_screen.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_screens/chapters_screen.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/dartMain/dartMain_screens/project_settings_screen.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';

class ProjectDetailsWidget extends ConsumerWidget {
  static const String routeName = '/projectDetailsMain';

  // Information text for each section
  final Map<String, String> sectionInfo = {
    "Trunk":
        "The Trunk section contains the main content of your project. Here you can organize chapters, articles, and other primary content for your readers.",
    "Branches":
        "The Branches section allows you to upload supplemental material that can be paired with the main content in the Trunk section. This helps you organize additional resources and expanded content for your readers.",
    "Community":
        "The Community section is where you can engage with your readers. Manage discussions, respond to feedback, and build your project community.",
    "Project Settings":
        "Manage your project settings, including visibility, applications, and other configuration options. This section is only available to you as the project owner."
  };

  final UniversalWidgets universals = UniversalWidgets();
  final FetchDatabase fetch = FetchDatabase();

  void desc(WidgetRef ref) async {
    ref.read(projectDescriptionProvider.notifier).state =
        await fetch.fetchProjectDescription(ref.watch(currentProjectID));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    desc(ref);
    final planType = ref.watch(planTypeProvider);
    final projectName = ref.read(currentProjectName.notifier).state;

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          projectName,
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        actions: [
          // Owner badge in the app bar
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color.fromARGB(25, 44, 148, 44),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: const Color.fromARGB(76, 44, 148, 44),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.person_outline,
                  size: 14,
                  color: Color.fromARGB(255, 44, 148, 44),
                ),
                const SizedBox(width: 4),
                Text(
                  'Owner',
                  style: TextStyle(
                    fontSize: 12,
                    color: globals.bookBranchGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Project description section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Color.fromARGB(25, 0, 0, 0),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: const Color.fromARGB(76, 44, 148, 44),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 18,
                      color: globals.bookBranchGreen,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Project Management',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: globals.bookBranchGreen,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'Manage your project content, engage with your community, and configure project settings.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color.fromARGB(204, 0, 0, 0),
                  ),
                ),
              ],
            ),
          ),
          // Main content sections
          Expanded(
            child: FutureBuilder<int>(
              future: Future.value(1),
              builder: (context, snapshot) {
                return GridView.count(
                  crossAxisCount: 1,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  padding: const EdgeInsets.all(16.0),
                  childAspectRatio: 3 / 1.5,
                  children: [
                    // Trunk section
                    universals.buildEnhancedColorfulCard(
                      context,
                      "Trunk",
                      sectionInfo["Trunk"]!,
                      () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "Database";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return ChaptersScreen();
                        }));
                      },
                      customIcon: Icons.menu_book,
                    ),
                    // Branches section
                    universals.buildEnhancedColorfulCard(
                      context,
                      "Branches",
                      sectionInfo["Branches"]!,
                      () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "E-Publishing";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return EPublishingScreen();
                        }));
                      },
                      customIcon: Icons.source,
                    ),
                    // Community section
                    universals.buildEnhancedColorfulCard(
                      context,
                      "Community",
                      sectionInfo["Community"]!,
                      () {
                        ref.read(currentProjectSubSection.notifier).state =
                            "Community";
                        Navigator.of(context)
                            .push(MaterialPageRoute(builder: (context) {
                          return DiscussionsScreen();
                        }));
                      },
                      customIcon: Icons.forum,
                    ),
                    // Project Settings section - special styling for owner-only section
                    _buildProjectSettingsCard(context, ref),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Custom card for Project Settings to highlight it as an owner-only feature
  Widget _buildProjectSettingsCard(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);
    const Color lightGreen = Color.fromARGB(25, 44, 148, 44);

    return GestureDetector(
      onTap: () {
        ref.read(currentProjectSubSection.notifier).state = "Project Settings";
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return ProjectSettingsScreen();
        }));
      },
      child: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromARGB(25, 0, 0, 0),
              spreadRadius: 1,
              blurRadius: 6,
              offset: Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: bookBranchGreen,
            width: 1.5,
          ),
        ),
        child: Stack(
          children: [
            // Decorative element - top gradient bar
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 8,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      bookBranchGreen,
                      Color.fromARGB(178, 44, 148, 44),
                    ],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
            ),
            // Decorative element - corner accent
            Positioned(
              top: 8,
              right: 0,
              child: Container(
                height: 30,
                width: 30,
                decoration: const BoxDecoration(
                  color: lightGreen,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                  ),
                ),
              ),
            ),
            // Section icon
            const Positioned(
              top: 50,
              left: 16,
              child: Icon(
                Icons.settings,
                size: 40,
                color: bookBranchGreen,
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.fromLTRB(70, 16, 16, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "Project Settings",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: bookBranchGreen,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    sectionInfo["Project Settings"]!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black54,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
