import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class FetchProjectData {
  Future<List<String>> fetchProjectTags(WidgetRef ref) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    List<String> tags = [];
    try {
      final querySnapshot = await db
          .collection("projects")
          .where('Project ID', isEqualTo: ref.watch(currentProjectID))
          .get();
      if (querySnapshot.docs.isNotEmpty) {
        ///final docId = querySnapshot.docs.first.id;
        tags = List<String>.from(querySnapshot.docs.first['tags']);
        print("Document updated successfully.");
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
    return tags;
  }

  Future<int> fetchNumberOfSubscribers(WidgetRef ref) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    int numberOfSubscribers = 0;
    try {
      String projectId = ref.watch(currentProjectID);
      final documentSnapshot =
          await db.collection("projects").doc(projectId).get();

      if (documentSnapshot.exists) {
        var data = documentSnapshot.data();
        if (data != null && data.containsKey('Subscribers')) {
          numberOfSubscribers = data['Subscribers'] as int;
          print("Subscribers count: $numberOfSubscribers");
        } else {
          print("Subscribers field not found in document");
        }
      } else {
        print("Project document not found for ID: $projectId");
      }
    } catch (e) {
      print("Error fetching subscribers: $e");
    }
    return numberOfSubscribers;
  }

  // Future<double> fetchStorageUsedByProject(WidgetRef ref) async {
  //   FirebaseFirestore db = FirebaseFirestore.instance;
  //   int storageUsedBytes = 0;
  //   double storageUsedGB = 0.0;
  //   try {
  //     final querySnapshot = await db
  //         .collection("projects")
  //         .where('Project ID', isEqualTo: ref.watch(currentProjectID))
  //         .get();
  //     if (querySnapshot.docs.isNotEmpty) {
  //       storageUsedBytes = querySnapshot.docs.first.get('Storage Used');
  //       storageUsedGB =
  //           storageUsedBytes / 1073741824; // Convert bytes to gigabytes
  //       print("Storage used in GB: $storageUsedGB");
  //     } else {
  //       // print("No matching document found.");
  //     }
  //   } catch (e) {
  //     print("Error fetching storage used: $e");
  //   }
  //   return storageUsedGB;
  // }

  Future<double> fetchStorageUsedByProject(WidgetRef ref) async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    double storageUsedGB = 0.0;
    try {
      String projectId = ref.watch(currentProjectID);
      final documentSnapshot =
          await db.collection("projects").doc(projectId).get();

      if (documentSnapshot.exists) {
        var data = documentSnapshot.data();
        if (data != null && data.containsKey('Storage Used')) {
          var storageUsed = data['Storage Used'];

          // Handle both int and double types
          double storageUsedBytes;
          if (storageUsed is int) {
            storageUsedBytes = storageUsed.toDouble();
          } else if (storageUsed is double) {
            storageUsedBytes = storageUsed;
          } else {
            throw Exception(
                "Unexpected type for 'Storage Used': ${storageUsed.runtimeType}");
          }

          storageUsedGB =
              storageUsedBytes / 1073741824; // Convert bytes to gigabytes
          print(
              "Storage used in bytes: $storageUsedBytes, in GB: $storageUsedGB");
        } else {
          print("Storage Used field not found in document");
        }
      } else {
        print("Project document not found for ID: $projectId");
      }
    } catch (e) {
      print("Error fetching storage used: $e");
    }
    return storageUsedGB;
  }
}
