import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';

class UpdateFileMetadata {
  Future<void> updateFileDisplayName(
      String fileUrl, String newDisplayName, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'displayName': newDisplayName,
        });
        print("Document updated successfully.");
        ref.refresh(fileMetadataProvider);
        ref.refresh(fileMetadataProviderEPublishing);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> updateFileDescription(
      String fileUrl, String newDescription, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'fileDescription': newDescription,
        });
        print("Document updated successfully.");
        ref.refresh(fileMetadataProvider);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> updateFileDescriptionEPublishing(
      String fileUrl, String newDescription, WidgetRef ref) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'displayName' field for that document
        await dataCollection.doc(docId).update({
          'fileDescription': newDescription,
        });
        print("Document updated successfully.");
        ref.refresh(fileMetadataProviderEPublishing);
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> uploadFileTagToFirestore(String fileUrl, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');
    //final file = FileMetadata.fetchSingleFileMetadataByUrl(fileUrl);

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'tags' field for that document
        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayUnion([tag]),
        });
        print("Document updated successfully.");
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }

  Future<void> removeFileTagFromFirestore(String fileUrl, String tag) async {
    final CollectionReference dataCollection =
        FirebaseFirestore.instance.collection('data');
    //final file = FileMetadata.fetchSingleFileMetadataByUrl(fileUrl);

    try {
      // Query the documents for the one containing the matching 'File URL'
      final querySnapshot =
          await dataCollection.where('File URL', isEqualTo: fileUrl).get();

      // Check if the document exists
      if (querySnapshot.docs.isNotEmpty) {
        // Assuming there's only one document with the matching 'File URL'
        final docId = querySnapshot.docs.first.id;

        // Update the 'tags' field for that document
        await dataCollection.doc(docId).update({
          'tags': FieldValue.arrayRemove([tag]),
        });
        print("Document updated successfully.");
      } else {
        //print("No matching document found.");
      }
    } catch (e) {
      print("Error updating document: $e");
    }
  }
}
