import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FetchDatabase {
  FirebaseFirestore db = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  Future<int> fetchChapterCount(WidgetRef ref) async {
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(await ref.watch(currentProjectID))
        .collection("chapters")
        .get();

    return snapshot.docs.length;
  }

  Future<int> fetchNumProjectsCreated() async {
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    DocumentSnapshot snapshot = await db.collection('users').doc(userUid).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Projects Created') == true) {
      return data!['Projects Created'] as int;
    } else {
      return 0;
    }
  }

  // there are two versions of this method (see below) to accomodate security rules
  Future<int> fetchProjectCount() async {
    QuerySnapshot snapshot = await db.collection("projects").get();
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    List<QueryDocumentSnapshot<Object?>> uidProjects = [];

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
      if (data['uid'] == userUid) {
        uidProjects.add(doc);
      }
    }
    return uidProjects.length;
  }

  Future<int> fetchProjectCountForDeletion() async {
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    DocumentSnapshot snapshot = await db.collection('users').doc(userUid).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Projects Created') == true) {
      return data!['Project Count'] as int;
    } else {
      return 0;
    }
  }

  Future<List<String>> fetchAllChapterTitles(WidgetRef ref) async {
    List<String> chapterTitles = [];

    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("chapters")
        .get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('title')) {
        String title = data['title'];
        chapterTitles.add(title);
      } else {
        print("Document ${doc.id} does not contain a 'title' field.");
      }
    }
    return chapterTitles;
  }

  Future<List<String>> fetchAllEPublishingChapterTitles(WidgetRef ref) async {
    List<String> chapterTitles = [];

    String projectID = ref.watch(currentProjectID);

    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("epublishing")
        .get();

    for (var doc in snapshot.docs) {
      Map<String, dynamic> data = doc.data() as Map<String, dynamic>;

      if (data.containsKey('title')) {
        String title = data['title'];
        chapterTitles.add(title);
      } else {
        print("Document ${doc.id} does not contain a 'title' field.");
      }
    }
    return chapterTitles;
  }

  Future<List<String>> fetchDocNames(WidgetRef ref, String sectionType) async {
    List<String> chapterDocs = [];
    String projectID = ref.watch(currentProjectID);
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection(sectionType)
        .get();

    for (var doc in snapshot.docs) {
      // Extract the 'title' field and add it to the list
      chapterDocs.add(doc.id);
    }
    return chapterDocs;
  }

  Future<List<String>> fetchDocNamesChaptersWidget(String projectID) async {
    List<String> chapterDocs = [];
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("chapters")
        .get();

    for (var doc in snapshot.docs) {
      chapterDocs.add(doc.id);
    }

    return chapterDocs;
  }

  Future<List<String>> fetchDocNamesEPublishingChaptersWidget(
      String projectID) async {
    List<String> chapterDocs = [];
    QuerySnapshot snapshot = await db
        .collection("projects")
        .doc(projectID)
        .collection("epublishing")
        .get();

    for (var doc in snapshot.docs) {
      chapterDocs.add(doc.id);
    }

    return chapterDocs;
  }

  Future<String> fetchDisplayName() async {
    String username = "";
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    if (userUid != null && userUid.isNotEmpty) {
      DocumentSnapshot userDoc =
          await db.collection('users').doc(userUid).get();
      Map<String, dynamic>? userData = userDoc.data() as Map<String, dynamic>?;
      if (userData != null && userData.containsKey('Display Name')) {
        username = userData['Display Name'] as String;
      } else {
        print('The user document does not have a username field or is null.');
      }
    } else {
      print('No user is currently signed in or the UID is null.');
    }

    return username;
  }

  Future<String> fetchProjectDescription(String projectID) async {
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Description') == true) {
      return data!['Description'] as String;
    } else {
      return 'No description available';
    }
  }

  Future<String> fetchProjectTitle(String projectID) async {
    String title = "";
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Project Name') == true) {
      title = data!['Project Name'] as String;
    }
    return title;
  }

  Future<String> fetchPlanType() async {
    String? userUid = FirebaseAuth.instance.currentUser?.uid;
    DocumentSnapshot snapshot = await db.collection('users').doc(userUid).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Account Type') == true) {
      return data!['Account Type'] as String;
    } else {
      return 'could not get plan type';
    }
  }

  // Future<double> fetchStorageUsedByUser() async {
  //   FirebaseFirestore db = FirebaseFirestore.instance;
  //   int storageUsedBytes = 0;
  //   double storageUsedGB = 0.0;
  //   String? userUid = FirebaseAuth.instance.currentUser?.uid;

  //   try {
  //     final querySnapshot =
  //         await db.collection("users").where('uid', isEqualTo: userUid).get();
  //     if (querySnapshot.docs.isNotEmpty) {
  //       storageUsedBytes = querySnapshot.docs.first.get('Storage Used');
  //       storageUsedGB =
  //           storageUsedBytes / **********; // Convert bytes to gigabytes
  //       print("Storage used in GB: $storageUsedGB");
  //     } else {
  //       // print("No matching document found.");
  //     }
  //   } catch (e) {
  //     print("Error fetching storage used: $e");
  //   }
  //   return storageUsedGB;
  // }
  Future<double> fetchStorageUsedByUser() async {
    FirebaseFirestore db = FirebaseFirestore.instance;
    double storageUsedGB = 0.0;
    String? userUid = FirebaseAuth.instance.currentUser?.uid;

    try {
      final querySnapshot =
          await db.collection("users").where('uid', isEqualTo: userUid).get();
      if (querySnapshot.docs.isNotEmpty) {
        var storageUsed = querySnapshot.docs.first.get('Storage Used');

        // Handle both int and double types
        double storageUsedBytes;
        if (storageUsed is int) {
          storageUsedBytes = storageUsed.toDouble();
        } else if (storageUsed is double) {
          storageUsedBytes = storageUsed;
        } else {
          throw Exception("Unexpected type for 'Storage Used'");
        }

        storageUsedGB =
            storageUsedBytes / **********; // Convert bytes to gigabytes
        print("Storage used in GB: $storageUsedGB");
      } else {
        print("No matching document found.");
      }
    } catch (e) {
      print("Error fetching storage used: $e");
    }
    return storageUsedGB;
  }

  Future<String> fetchProjectVisibility(String projectID) async {
    DocumentSnapshot snapshot =
        await db.collection('projects').doc(projectID).get();
    Map<String, dynamic>? data = snapshot.data() as Map<String, dynamic>?;
    if (snapshot.exists && data?.containsKey('Visibility') == true) {
      return data!['Visibility'] as String;
    } else {
      return 'No description available';
    }
  }

  Future<int> fetchNumberOfFilesBySubsection(
      String chapter, String subsection) async {
    QuerySnapshot snapshot = await db
        .collection('data')
        .where('chapter', isEqualTo: chapter)
        .where('subsection', isEqualTo: subsection)
        .get();

    return snapshot.docs.length;
  }

  Future<int> fetchNumberOfFilesByEPublishingSubsection(
      String chapter, String subsection) async {
    QuerySnapshot snapshot = await db
        .collection('data')
        .where('chapter', isEqualTo: chapter)
        .where('subsection', isEqualTo: subsection)
        .get();

    return snapshot.docs.length;
  }
}
