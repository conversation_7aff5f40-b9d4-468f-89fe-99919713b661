import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_widgets/file_details_widget.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_widgets/display_video_widget.dart';
import 'package:web_app/readerMode/database_screens/file_comments_screen_reader.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:photo_view/photo_view.dart';

class FileDetailsWidgetReader extends ConsumerWidget {
  final String thumbnailUrl =
      "assets/no_data.png"; // Assuming this is available

  final String fileUrl; // Initialize with the URL of the file

  FileDetailsWidgetReader({required this.fileUrl});

  UniversalWidgets universals = UniversalWidgets();
  TextEditingController input = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const Color bookBranchGreen = Color.fromARGB(255, 44, 148, 44);

    return Scaffold(
      appBar: AppBar(
        title: Text(ref.watch(currentProjectName)),
      ),
      body: FutureBuilder<FileMetadata?>(
        future: FileMetadata.fetchSingleFileMetadataByUrl(fileUrl),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return const Center(child: Text("Error loading file details"));
          } else if (snapshot.data == null) {
            return const Center(child: Text("No file details found"));
          } else {
            // Successfully fetched file metadata
            FileMetadata fileMetadata = snapshot.data!;
            return Column(
              children: [
                // Assuming you want to display the image or a placeholder
                Expanded(
                  flex: 1,
                  child: InkWell(
                    onTap: () {
                      // Navigate to a full-screen view or handle the tap as needed
                      switch (fileMetadata.fileType) {
                        case 'Image':
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  FullScreenMediaView(fileMetadata.fileURL),
                            ),
                          );
                          break;
                        case 'PDF':
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  MobilePdfViewer(pdfUrl: fileMetadata.fileURL),
                            ),
                          );
                          break;
                        case 'Video':
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DisplayVideoWidget(
                                  videoUrl: fileMetadata.fileURL),
                            ),
                          );
                          break;
                        default:
                          // Handle unknown media type
                          break;
                      }
                    },
                    child: fileMetadata.fileType == 'Image'
                        ? Image.network(
                            fileMetadata.fileURL,
                            fit: BoxFit.cover,
                            width: double.infinity,
                          )
                        : fileMetadata.fileType == 'Video'
                            ? Image.asset(
                                'assets/play_image.png', // Placeholder image for videos
                                fit: BoxFit.cover,
                                width: double.infinity,
                              )
                            : fileMetadata.fileType == 'PDF'
                                ? Image.asset(
                                    'assets/pdf_icon.png', // Placeholder image for PDFs
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                  )
                                : const SizedBox(),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(
                        16), // Added padding between the border and the edge of the screen
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: bookBranchGreen,
                          width: 1, // Thin green line encompassing all content
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Stack(
                        children: [
                          SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          fileMetadata
                                              .displayName, // Display file name as title
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: universals.buildButtonFlat(
                                        "Comments",
                                        true,
                                        () async {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  FileCommentsScreenReader(),
                                            ),
                                          );
                                        },
                                        bookBranchGreen,
                                        Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 25),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  child: Text(
                                    fileMetadata
                                        .fileDescription, // Display file description
                                    style:
                                        Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}

class FullScreenMediaView extends StatelessWidget {
  final String imageUrl;

  const FullScreenMediaView(this.imageUrl, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: Center(
        child: PhotoView(
          imageProvider: NetworkImage(imageUrl),
          backgroundDecoration: BoxDecoration(
            color: Theme.of(context)
                .scaffoldBackgroundColor, // Match the background color to the scaffold
          ),
          minScale: PhotoViewComputedScale.contained * 0.8, // Minimum scale
          maxScale: PhotoViewComputedScale.covered * 2, // Maximum scale
          enableRotation: false, // Optionally enable rotation
        ),
      ),
    );
  }
}
