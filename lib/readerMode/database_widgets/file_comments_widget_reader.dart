import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/readerMode/database_logic/database_logic_fetch/fetch_file_comments_reader.dart';
import 'package:web_app/readerMode/database_logic/file_comments_logic_reader.dart';
import 'package:intl/intl.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/update_file_metadata.dart';
import 'package:web_app/readerMode/database_logic/file_comments_state_management_reader.dart';
import 'package:tuple/tuple.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class FileCommentsWidgetReader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Comment Section',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: globals.bookBranchGreen),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: CommentSection(),
    );
  }
}

class CommentSection extends ConsumerWidget {
  FetchFileCommentsReader fetchFileCommentsReader = FetchFileCommentsReader();
  FileCommentsLogicReader fileCommentsLogicReader = FileCommentsLogicReader();
  UniversalWidgets universals = UniversalWidgets();
  UpdateFileMetadata metadata = UpdateFileMetadata();

  final TextEditingController _tagnameController = TextEditingController();
  final TextEditingController _tagnameControllerNested =
      TextEditingController();

  List<dynamic> commentIDList = []; // List to store likes values
  final FirebaseAuth auth = FirebaseAuth.instance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.watch(topLevelCommentsProvider(ref.watch(chosenFileUrl)));

    return Scaffold(
      body: FutureBuilder<List<List<dynamic>>>(
        future: fetchFileCommentsReader.fetchFileCommentData(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text("Error: ${snapshot.error}"));
          } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
            //ref.watch(topLevelCommentsProvider(ref.watch(chosenFileUrl)));
            final comments = snapshot.data!;
            return ListView.builder(
              itemCount: comments[0].length,
              itemBuilder: (context, index) {
                String formattedTime = DateFormat('yyyy-MM-dd – kk:mm')
                    .format(comments[1][index].toDate()); // Format timestamp

                commentIDList.add(comments[4][index]); // Add commentID to list

                return CommentCard(
                  author: comments[0][index],
                  time: formattedTime,
                  content: comments[2][index],
                  likes: fetchFileCommentsReader
                      .fetchNumberOfLikes(comments[4][index]),
                  commentId: comments[4][index],
                  fileUrl: ref.watch(chosenFileUrl),
                  numReplies: fetchFileCommentsReader.fetchNumberOfReplies(
                      comments[4][index], ref.watch(chosenFileUrl)),
                  onPressed: () {
                    // please note that a ref.refresh() is called within the postNestedFileComment() method itself
                    // without this the UI does not update appropriately
                    print("Comment ID: ${comments[4][index]}");
                    universals.showCustomPopupWithTextfield(
                        context,
                        "Post a Comment",
                        "Write your message here",
                        _tagnameControllerNested, () async {
                      await fileCommentsLogicReader.postNestedFileComment(
                          _tagnameControllerNested.text,
                          comments[4][index],
                          ref);
                      _tagnameControllerNested
                          .clear(); // Clear the text field after submission
                      showNestedComments(comments[4][index], context, ref);
                    });
                  },
                  onPressedViewReplies: () {
                    showNestedComments(comments[4][index], context, ref);
                  },
                  onPressedLike: () {
                    fileCommentsLogicReader.likeComment(
                        comments[4][index], auth.currentUser!.uid, ref);
                    ref.refresh(fileCommentsLikesProvider((comments[4]
                        [index]))); // Refresh the number of likes displayed
                  },
                  onPressedDislike: () {
                    fileCommentsLogicReader.dislikeComment(
                        comments[4][index], auth.currentUser!.uid, ref);
                    ref.refresh(
                        fileCommentsLikesProvider((comments[4][index])));
                  },
                );
              },
            );
          } else {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.0),
                    child: Text(
                      'No comments here yet. Be the first!',
                      style: TextStyle(fontSize: 18),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: 20),
                ],
              ),
            );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Implement your add comment action here
          universals.showCustomPopupWithTextfield(context, "Post a Comment",
              "Write your message here", _tagnameController, () async {
            await fileCommentsLogicReader.postFileComment(
                _tagnameController.text, ref);
            _tagnameController.clear(); // Clear the text field after submission
            ref.invalidate(topLevelCommentsProvider(ref.watch(chosenFileUrl)));
            ref.refresh(topLevelCommentsProvider(ref.watch(chosenFileUrl)));
          });
        },
        backgroundColor: globals.bookBranchGreen,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  void showNestedComments(
      String parentCommentID, BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
      ),
      backgroundColor: Colors.white, // Background color for the modal
      builder: (BuildContext bc) {
        return FutureBuilder<List<dynamic>>(
          future: fetchFileCommentsReader.fetchNestedComments(
              parentCommentID, ref.watch(chosenFileUrl), ref),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Text("Error: ${snapshot.error}");
            } else if (snapshot.hasData && snapshot.data!.isNotEmpty) {
              return ListView.builder(
                itemCount: snapshot.data!.length,
                itemBuilder: (context, index) {
                  var nestedComment = snapshot.data![index];
                  return Consumer(
                    builder: (context, ref, child) {
                      final likes = ref.watch(fileCommentsNestedLikesProvider(
                          Tuple2(parentCommentID, nestedComment['commentID'])));
                      return Container(
                        margin: const EdgeInsets.all(8.0),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.zero,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              spreadRadius: 2,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            color: Colors.black, // Thin black outline
                            width: 1, // Thickness of the outline
                          ),
                        ),
                        padding: const EdgeInsets.all(10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(nestedComment['displayName'],
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold)),
                            Text(nestedComment['comment']),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.thumb_up, size: 20),
                                  onPressed: () {
                                    fileCommentsLogicReader.likeNestedComment(
                                        parentCommentID,
                                        nestedComment['commentID'],
                                        auth.currentUser!.uid,
                                        ref);
                                  },
                                  color: Colors.grey,
                                ),
                                likes.when(
                                  data: (likesCount) => Text(
                                      likesCount.toString(),
                                      style:
                                          const TextStyle(color: Colors.grey)),
                                  loading: () => const Text('Loading...',
                                      style: TextStyle(color: Colors.grey)),
                                  error: (error, stack) => const Text('Error',
                                      style: TextStyle(color: Colors.grey)),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.thumb_down, size: 20),
                                  onPressed: () {
                                    fileCommentsLogicReader
                                        .dislikeNestedComment(
                                            parentCommentID,
                                            nestedComment['commentID'],
                                            auth.currentUser!.uid,
                                            ref);
                                  },
                                  color: Colors.grey,
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
              );
            } else {
              return const Center(child: Text("No replies found"));
            }
          },
        );
      },
    );
  }
}

class CommentCard extends ConsumerWidget {
  final String author;
  final String time;
  final String content;
  final Future<int> likes;
  final List<Widget> replies; // This will hold nested CommentCards
  final Future<int> numReplies;
  final String commentId;
  final String fileUrl;

  final VoidCallback onPressed;
  final VoidCallback onPressedViewReplies;
  final VoidCallback onPressedLike;
  final VoidCallback onPressedDislike;

  const CommentCard({
    Key? key,
    required this.author,
    required this.time,
    required this.content,
    required this.likes,
    required this.commentId,
    required this.fileUrl,
    required this.onPressed,
    required this.onPressedViewReplies,
    required this.onPressedLike,
    required this.onPressedDislike,
    required this.numReplies,
    this.replies = const [],
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final numReplies =
        ref.watch(fileCommentsRepliesProvider(Tuple2(commentId, fileUrl)));

    final likes = ref.watch(fileCommentsLikesProvider(commentId));

    return Container(
      margin: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.zero,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.black, // Thin black outline
          width: 1, // Thickness of the outline
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  author,
                  style: const TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.thumb_up, size: 20),
                      onPressed: onPressedLike,
                      color: Colors.grey,
                    ),
                    likes.when(
                      data: (replies) => Text(replies.toString(),
                          style: const TextStyle(color: Colors.grey)),
                      loading: () => const Text("Loading...",
                          style: TextStyle(color: Colors.grey)),
                      error: (error, stack) => const Text('Error',
                          style: TextStyle(color: Colors.grey)),
                    ),
                    IconButton(
                      icon: const Icon(Icons.thumb_down, size: 20),
                      onPressed: onPressedDislike,
                      color: Colors.grey,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              time,
              style: const TextStyle(
                fontSize: 12.0,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              content,
              style: const TextStyle(
                fontSize: 14.0,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  icon: const Icon(Icons.reply, size: 20),
                  onPressed: onPressed,
                  color: Colors.grey,
                ),
                const Text('Respond', style: TextStyle(color: Colors.grey)),
                IconButton(
                  icon: const Icon(Icons.question_answer, size: 20),
                  onPressed: onPressedViewReplies,
                  color: Colors.grey,
                ),
                numReplies.when(
                  data: (replies) => Text(replies.toString(),
                      style: const TextStyle(color: Colors.grey)),
                  loading: () => const Text("Loading...",
                      style: TextStyle(color: Colors.grey)),
                  error: (error, stack) =>
                      const Text('Error', style: TextStyle(color: Colors.grey)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
