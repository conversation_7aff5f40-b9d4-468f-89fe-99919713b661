import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/readerMode/database_screens/file_details_screen_reader.dart';
import 'package:web_app/database/database_widgets/elevatedButtons.dart';
import 'package:web_app/database/database_widgets/subsections_widget.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/file_metadata_fetch.dart';
import 'package:web_app/database/database_logic/delete_data.dart';
import 'package:web_app/main.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/widgets/nav_drawer_plus.dart';
import 'package:web_app/widgets/nav_drawer_basic.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class EPublishingFilesWidgetReader extends ConsumerWidget {
  static const String routeName = '/databaseChapterDetails';
  final ElevatedButtons elevatedButton = ElevatedButtons();
  final SubsectionsWidget widget = SubsectionsWidget();

  final DeleteData deleteData = DeleteData();
  final FetchDatabase fetch = FetchDatabase();

  final Map<String, IconData> fileTypeIcons = {
    'Image': Icons.image, // Icon for image files
    'PDF': Icons.picture_as_pdf, // Icon for PDF files
    'Video': Icons.videocam, // Icon for video files
    // Add more file types and their icons as needed
  };

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentChapterVar = ref.watch(currentEPublishingChapter);
    final currentSubsectionVar = ref.watch(currentEPublishingSubsection);
    final currentProjectIDVar = ref.watch(currentProjectID);
    final planType = ref.watch(planTypeProvider);

    return Scaffold(
      drawer: planType == 'BookBranch+'
          ? NavDrawerPlus(currentRoute: routeName)
          : NavDrawerBasic(currentRoute: routeName),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            height: 2.0,
            color: globals.bookBranchGreen,
          ),
        ),
        title: Text(
          'Database',
          style: TextStyle(
            color: globals.bookBranchGreen,
            fontWeight: FontWeight.w600,
            fontSize: 20,
            letterSpacing: 0.5,
          ),
        ),
        iconTheme: IconThemeData(color: globals.bookBranchGreen),
      ),
      body: FutureBuilder<List<FileMetadata>>(
        future: FileMetadata.fetchFileMetadataEPublishing(
            currentChapterVar, currentSubsectionVar, currentProjectIDVar),
        builder:
            (BuildContext context, AsyncSnapshot<List<FileMetadata>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          } else if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          } else if (snapshot.hasData) {
            if (snapshot.data!.isEmpty) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  child: Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(maxWidth: 400),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(10),
                          blurRadius: 10,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: globals.bookBranchGreen.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'No Files Available',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'There is no content available here yet.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Files uploaded to this section will appear here.',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else {
              return kIsWeb
                  ? buildGridView(snapshot.data!, ref)
                  : buildListViewMobile(
                      snapshot.data!, ref); // Use ListView for mobile
            }
          } else {
            return const Center(
              child: Text('No data available'),
            );
          }
        },
      ),
    );
  }

  Widget buildGridView(List<FileMetadata> files, WidgetRef ref) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 3 / 1.2,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: files.length,
      itemBuilder: (context, index) {
        FileMetadata metadata = files[index];
        IconData iconData =
            fileTypeIcons[metadata.fileType] ?? Icons.file_present;
        return buildFileCard(context, ref, metadata, iconData);
      },
    );
  }

  Widget buildListViewMobile(List<FileMetadata> files, WidgetRef ref) {
    return ListView.builder(
      itemCount: files.length,
      itemBuilder: (context, index) {
        FileMetadata metadata = files[index];
        IconData iconData =
            fileTypeIcons[metadata.fileType] ?? Icons.file_present;
        return buildFileCardMobile(context, ref, metadata, iconData);
      },
    );
  }

  Widget buildFileCard(BuildContext context, WidgetRef ref,
      FileMetadata metadata, IconData iconData) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () {
          String fileUrl = metadata.fileURL;
          ref.read(chosenFileUrl.notifier).state = fileUrl;

          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return FilesDetailsScreenReader(
                fileUrl: fileUrl); // Pass fileUrl here
          }));
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                metadata.displayName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  metadata.fileDescription,
                  overflow: TextOverflow.ellipsis, // Add this
                  maxLines:
                      3, // Set max lines or adjust according to your needs
                  style: const TextStyle(
                      fontSize: 14), // Optional: Adjust text style
                ),
              ),
              const Spacer(), // Pushes the icon to the bottom of the card
              Icon(iconData, size: 50)
            ],
          ),
        ),
      ),
    );
  }

  Widget buildFileCardMobile(BuildContext context, WidgetRef ref,
      FileMetadata metadata, IconData iconData) {
    return Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.zero,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26), // Equivalent to 0.1 opacity
              spreadRadius: 2,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.black, // Thin black outline
            width: 1, // Thickness of the outline
          ),
        ),
        width: double.infinity, // Ensure the container takes up the full width
        child: InkWell(
          onTap: () {
            String fileUrl = metadata.fileURL;
            ref.read(chosenFileUrl.notifier).state = fileUrl;

            Navigator.of(context).push(MaterialPageRoute(builder: (context) {
              return FilesDetailsScreenReader(
                  fileUrl: fileUrl); // Pass fileUrl here
            }));
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  metadata.displayName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  metadata.fileDescription,
                  style: const TextStyle(
                      fontSize: 14), // Optional: Adjust text style
                ),
                const SizedBox(height: 16), // Adds space before the icon
                Icon(iconData, size: 25),
              ],
            ),
          ),
        ));
  }
}
